import { Injectable } from '@nestjs/common';
import { RedisService } from 'libs/redis/src';
import { InjectPinoLogger, PinoLogger } from 'nestjs-pino';
import * as crypto from 'crypto';

export interface RateLimitConfig {
    windowMs: number; // Time window in milliseconds
    maxRequests: number; // Maximum requests per window
    keyPrefix: string; // Redis key prefix for this rate limit type
}

export interface RateLimitResult {
    allowed: boolean;
    remainingRequests: number;
    resetTime: number; // Unix timestamp when the window resets
    totalHits: number;
}

@Injectable()
export class RateLimitingService {
    constructor(
        private readonly redisService: RedisService,
        @InjectPinoLogger(RateLimitingService.name)
        private readonly logger: PinoLogger,
    ) {}

    /**
     * Hash IP address for privacy while maintaining uniqueness
     */
    private hashIpAddress(ipAddress: string): string {
        return crypto
            .createHash('sha256')
            .update(ipAddress + 'rate_limit_salt')
            .digest('hex');
    }

    /**
     * Check if a request is allowed based on rate limiting rules
     */
    async checkRateLimit(ipAddress: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIp = this.hashIpAddress(ipAddress);
        const redisKey = `${config.keyPrefix}:${hashedIp}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Remove expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Count current requests in window
            pipeline.zcard(redisKey);

            // Add current request
            pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

            // Set expiration for the key
            pipeline.expire(redisKey, Math.ceil(config.windowMs / 1000));

            const results = await pipeline.exec();

            if (!results || results.some(([err]) => err)) {
                this.logger.error('Redis pipeline execution failed for rate limiting');
                // Fail open - allow the request if Redis is having issues
                return {
                    allowed: true,
                    remainingRequests: config.maxRequests - 1,
                    resetTime: now + config.windowMs,
                    totalHits: 1,
                };
            }

            // Get the count after cleanup but before adding current request
            const currentCount = results[1][1] as number;
            const totalHits = currentCount + 1; // Including the current request we just added

            const allowed = totalHits <= config.maxRequests;
            const remainingRequests = Math.max(0, config.maxRequests - totalHits);
            const resetTime = now + config.windowMs;

            if (!allowed) {
                // Remove the request we just added since it's not allowed
                await redis.zrem(redisKey, `${now}-${Math.random()}`);

                this.logger.warn(
                    {
                        hashedIp,
                        totalHits,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                    },
                    'Rate limit exceeded',
                );
            }

            return {
                allowed,
                remainingRequests,
                resetTime,
                totalHits: allowed ? totalHits : currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIp,
                    config,
                },
                'Error checking rate limit',
            );

            // Fail open - allow the request if there's an error
            return {
                allowed: true,
                remainingRequests: config.maxRequests - 1,
                resetTime: now + config.windowMs,
                totalHits: 1,
            };
        }
    }

    /**
     * Get current rate limit status without incrementing
     */
    async getRateLimitStatus(ipAddress: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIp = this.hashIpAddress(ipAddress);
        const redisKey = `${config.keyPrefix}:${hashedIp}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Clean up expired entries and count current requests
            await redis.zremrangebyscore(redisKey, 0, windowStart);
            const currentCount = await redis.zcard(redisKey);

            const remainingRequests = Math.max(0, config.maxRequests - currentCount);
            const resetTime = now + config.windowMs;

            return {
                allowed: currentCount < config.maxRequests,
                remainingRequests,
                resetTime,
                totalHits: currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIp,
                    config,
                },
                'Error getting rate limit status',
            );

            return {
                allowed: true,
                remainingRequests: config.maxRequests,
                resetTime: now + config.windowMs,
                totalHits: 0,
            };
        }
    }

    /**
     * Reset rate limit for a specific IP and config
     */
    async resetRateLimit(ipAddress: string, config: RateLimitConfig): Promise<void> {
        const hashedIp = this.hashIpAddress(ipAddress);
        const redisKey = `${config.keyPrefix}:${hashedIp}`;

        try {
            await this.redisService.getClient().del(redisKey);
            this.logger.info({ hashedIp, keyPrefix: config.keyPrefix }, 'Rate limit reset');
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIp,
                    config,
                },
                'Error resetting rate limit',
            );
        }
    }

    /**
     * Hash any identifier for privacy while maintaining uniqueness
     */
    private hashIdentifier(identifier: string, salt: string = 'rate_limit_salt'): string {
        return crypto
            .createHash('sha256')
            .update(identifier + salt)
            .digest('hex');
    }

    /**
     * Check rate limit for a specific identifier (email, fingerprint, etc.)
     */
    async checkRateLimitByIdentifier(identifier: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIdentifier = this.hashIdentifier(identifier);
        const redisKey = `${config.keyPrefix}:${hashedIdentifier}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Use Redis pipeline for atomic operations
            const pipeline = redis.pipeline();

            // Remove expired entries
            pipeline.zremrangebyscore(redisKey, 0, windowStart);

            // Count current requests in window
            pipeline.zcard(redisKey);

            // Add current request
            pipeline.zadd(redisKey, now, `${now}-${Math.random()}`);

            // Set expiration for the key
            pipeline.expire(redisKey, Math.ceil(config.windowMs / 1000));

            const results = await pipeline.exec();

            if (!results || results.some(([err]) => err)) {
                this.logger.error('Redis pipeline execution failed for identifier rate limiting');
                // Fail open - allow the request if Redis is having issues
                return {
                    allowed: true,
                    remainingRequests: config.maxRequests - 1,
                    resetTime: now + config.windowMs,
                    totalHits: 1,
                };
            }

            // Get the count after cleanup but before adding current request
            const currentCount = results[1][1] as number;
            const totalHits = currentCount + 1; // Including the current request we just added

            const allowed = totalHits <= config.maxRequests;
            const remainingRequests = Math.max(0, config.maxRequests - totalHits);
            const resetTime = now + config.windowMs;

            if (!allowed) {
                // Remove the request we just added since it's not allowed
                await redis.zrem(redisKey, `${now}-${Math.random()}`);

                this.logger.warn(
                    {
                        hashedIdentifier,
                        totalHits,
                        maxRequests: config.maxRequests,
                        windowMs: config.windowMs,
                        keyPrefix: config.keyPrefix,
                    },
                    'Rate limit exceeded for identifier',
                );
            }

            return {
                allowed,
                remainingRequests,
                resetTime,
                totalHits: allowed ? totalHits : currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIdentifier: this.hashIdentifier(identifier),
                    config,
                },
                'Error checking rate limit by identifier',
            );

            // Fail open - allow the request if there's an error
            return {
                allowed: true,
                remainingRequests: config.maxRequests - 1,
                resetTime: now + config.windowMs,
                totalHits: 1,
            };
        }
    }

    /**
     * Get current rate limit status for a specific identifier without incrementing
     */
    async getRateLimitStatusByIdentifier(identifier: string, config: RateLimitConfig): Promise<RateLimitResult> {
        const hashedIdentifier = this.hashIdentifier(identifier);
        const redisKey = `${config.keyPrefix}:${hashedIdentifier}`;
        const now = Date.now();
        const windowStart = now - config.windowMs;

        try {
            const redis = this.redisService.getClient();

            // Clean up expired entries and count current requests
            await redis.zremrangebyscore(redisKey, 0, windowStart);
            const currentCount = await redis.zcard(redisKey);

            const remainingRequests = Math.max(0, config.maxRequests - currentCount);
            const resetTime = now + config.windowMs;

            return {
                allowed: currentCount < config.maxRequests,
                remainingRequests,
                resetTime,
                totalHits: currentCount,
            };
        } catch (error) {
            this.logger.error(
                {
                    error: error.message,
                    stack: error.stack,
                    hashedIdentifier,
                    config,
                },
                'Error getting rate limit status by identifier',
            );

            return {
                allowed: true,
                remainingRequests: config.maxRequests,
                resetTime: now + config.windowMs,
                totalHits: 0,
            };
        }
    }
}
